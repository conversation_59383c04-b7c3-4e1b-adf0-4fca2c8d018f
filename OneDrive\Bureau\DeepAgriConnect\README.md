# 🌾 AgriConnect - Agricultural Platform

A comprehensive agricultural platform that combines farm management, marketplace functionality, and supply chain optimization with AI, IoT, and blockchain integration.

## 🚀 Features

### 🏡 Farm Management
- **Crop Planning & Management**: Plan, track, and manage crop cycles
- **IoT Integration**: Real-time monitoring of soil, weather, and crop conditions
- **AI Recommendations**: Smart suggestions for planting, irrigation, and harvesting
- **Resource Management**: Track equipment, labor, and input costs

### 🛒 Marketplace
- **Product Listings**: Farmers can list their produce with detailed information
- **Supply Chain Tracking**: Blockchain-based traceability from farm to consumer
- **Price Discovery**: Real-time market prices and demand forecasting
- **Quality Verification**: AI-powered quality assessment and certification

### 🤝 Cooperatives
- **Group Management**: Organize farmers into cooperatives
- **Collective Selling**: Pool resources for better market access
- **Resource Sharing**: Share equipment, knowledge, and best practices
- **Financial Services**: Group lending and insurance options

### 📊 Analytics & Insights
- **Yield Predictions**: AI-powered crop yield forecasting
- **Market Analysis**: Price trends and demand patterns
- **Performance Metrics**: ROI, efficiency, and sustainability indicators
- **Weather Integration**: Real-time weather data and forecasts

## 🛠 Technology Stack

### Backend
- **Framework**: Express.js with TypeScript
- **Database**: SQLite with comprehensive schema
- **Authentication**: JWT with refresh tokens
- **Real-time**: Socket.io for live updates
- **AI Integration**: Custom AI services for recommendations
- **Blockchain**: Ethereum integration for traceability

### Frontend
- **Framework**: Next.js 14 with App Router
- **UI Library**: Shadcn UI components
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Real-time**: Socket.io client
- **Maps**: Leaflet for geospatial features

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Caching**: Redis for session and data caching
- **File Storage**: Local storage with cloud backup
- **Monitoring**: Winston logging and error tracking

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 8+
- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd DeepAgriConnect
   ```

2. **Install dependencies**
   ```bash
   npm run setup
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development servers**
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:4000
   - API Documentation: http://localhost:4000/api-docs

### Docker Deployment

1. **Build and start containers**
   ```bash
   npm run docker:build
   npm run docker:up
   ```

2. **View logs**
   ```bash
   npm run docker:logs
   ```

## 📁 Project Structure

```
agriconnect/
├── backend/                 # Express.js backend
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── middleware/     # Custom middleware
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   ├── database/       # Database configuration
│   │   └── types/          # TypeScript types
│   ├── tests/              # Backend tests
│   └── Dockerfile
├── frontend/               # Next.js frontend
│   ├── app/               # App router pages
│   ├── components/        # React components
│   ├── lib/              # Utility libraries
│   ├── hooks/            # Custom hooks
│   ├── store/            # State management
│   └── types/            # TypeScript types
├── nginx/                # Nginx configuration
├── docs/                 # Documentation
└── docker-compose.yml   # Multi-container setup
```

## 🔐 Default Credentials

**Admin User:**
- Email: <EMAIL>
- Password: Password123

**Test Farmer:**
- Email: <EMAIL>
- Password: Password123

**Test Buyer:**
- Email: <EMAIL>
- Password: Password123

## 📚 API Documentation

The API documentation is available at `/api-docs` when the backend is running. It includes:
- Authentication endpoints
- Farm management APIs
- Marketplace functionality
- IoT data endpoints
- AI recommendation services
- Blockchain integration

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests only
npm run test:backend

# Run frontend tests only
npm run test:frontend
```

## 🚀 Deployment

### AWS Deployment
1. Configure AWS credentials
2. Update environment variables for production
3. Deploy using Docker containers on ECS or EC2

### Environment Variables
See `.env.example` for all required environment variables.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [docs/](./docs/)

## 🔄 Version History

- **v1.0.0** - Initial release with core features
  - Farm management system
  - Marketplace functionality
  - IoT integration
  - AI recommendations
  - Blockchain traceability
