version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - PORT=4000
      - DATABASE_URL=/app/data/agriconnect.db
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASS=${EMAIL_PASS}
      - WEATHER_API_KEY=${WEATHER_API_KEY}
      - BLOCKCHAIN_NETWORK=${BLOC<PERSON>CHAIN_NETWORK}
      - AI_SERVICE_URL=${AI_SERVICE_URL}
    volumes:
      - backend_data:/app/data
      - backend_uploads:/app/uploads
    depends_on:
      - redis
    networks:
      - agriconnect_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:4000
      - NEXT_PUBLIC_SOCKET_URL=http://backend:4000
      - NEXT_PUBLIC_APP_NAME=AgriConnect
      - NEXT_PUBLIC_APP_VERSION=1.0.0
    depends_on:
      - backend
    networks:
      - agriconnect_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - agriconnect_network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - agriconnect_network

volumes:
  backend_data:
  backend_uploads:
  redis_data:

networks:
  agriconnect_network:
    driver: bridge
