{"name": "agriconnect", "version": "1.0.0", "description": "Full-featured agricultural platform combining farm management, marketplace, and supply chain optimization", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install", "seed": "cd backend && npm run seed", "migrate": "cd backend && npm run migrate", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:frontend": "cd frontend && rm -rf node_modules .next"}, "keywords": ["agriculture", "farm-management", "marketplace", "supply-chain", "iot", "blockchain", "ai", "typescript", "express", "nextjs"], "author": "AgriConnect Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}