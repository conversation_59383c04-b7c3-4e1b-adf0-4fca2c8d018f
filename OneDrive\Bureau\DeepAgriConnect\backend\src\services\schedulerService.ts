import cron from 'node-cron';
import { logger } from '@/utils/logger';

export function initializeScheduler(): void {
  try {
    logger.info('Initializing scheduler service');

    // Example: Run every hour
    cron.schedule('0 * * * *', () => {
      logger.info('Running hourly scheduled task');
      // Add scheduled tasks here
    });

    logger.info('Scheduler service initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize scheduler service:', error);
    // Don't throw error - scheduler is optional for basic functionality
  }
}
