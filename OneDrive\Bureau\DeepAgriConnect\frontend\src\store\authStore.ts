import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: 'admin' | 'farmer' | 'buyer' | 'cooperative' | 'logistics'
  isEmailVerified: boolean
  avatar?: string
  phone?: string
  address?: string
  city?: string
  country?: string
  zipCode?: string
  isActive: boolean
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role: 'farmer' | 'buyer' | 'cooperative' | 'logistics'
  phone?: string
  address?: string
  city?: string
  country?: string
  zipCode?: string
}

interface AuthState {
  user: User | null
  tokens: AuthTokens | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null })

          const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
          })

          const data = await response.json()

          if (!response.ok) {
            throw new Error(data.message || 'Invalid email or password')
          }

          if (data.success && data.data) {
            // Map backend user fields to frontend User interface
            const user: User = {
              id: data.data.user.id,
              email: data.data.user.email,
              firstName: data.data.user.first_name,
              lastName: data.data.user.last_name,
              role: data.data.user.role,
              phone: data.data.user.phone,
              address: data.data.user.address,
              city: data.data.user.city,
              country: data.data.user.country,
              zipCode: data.data.user.zip_code,
              isEmailVerified: data.data.user.is_email_verified,
              isActive: data.data.user.is_active,
              createdAt: data.data.user.created_at,
              updatedAt: data.data.user.updated_at
            }

            set({
              user,
              tokens: data.data.tokens,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            throw new Error('Invalid response format')
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          })
          throw error
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true, error: null })

          const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          })

          const result = await response.json()

          if (!response.ok) {
            throw new Error(result.message || 'Registration failed')
          }

          if (result.success && result.data) {
            // Map backend user fields to frontend User interface
            const user: User = {
              id: result.data.user.id,
              email: result.data.user.email,
              firstName: result.data.user.first_name,
              lastName: result.data.user.last_name,
              role: result.data.user.role,
              phone: result.data.user.phone,
              address: result.data.user.address,
              city: result.data.user.city,
              country: result.data.user.country,
              zipCode: result.data.user.zip_code,
              isEmailVerified: result.data.user.is_email_verified,
              isActive: result.data.user.is_active,
              createdAt: result.data.user.created_at,
              updatedAt: result.data.user.updated_at
            }

            set({
              user,
              tokens: result.data.tokens,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            throw new Error('Invalid response format')
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Registration failed',
          })
          throw error
        }
      },

      logout: async () => {
        try {
          const { tokens } = get()
          
          // Call logout endpoint if we have tokens
          if (tokens) {
            await fetch(`${API_BASE_URL}/api/auth/logout`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${tokens.accessToken}`,
              },
            })
          }
        } catch (error) {
          console.error('Logout API call failed:', error)
        } finally {
          // Clear state regardless of API call success
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            error: null,
          })
        }
      },

      refreshToken: async () => {
        try {
          const { tokens } = get()
          
          if (!tokens?.refreshToken) {
            throw new Error('No refresh token available')
          }

          const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ refreshToken: tokens.refreshToken }),
          })

          const data = await response.json()

          if (!response.ok) {
            throw new Error(data.message || 'Token refresh failed')
          }

          if (data.success && data.data?.tokens) {
            set({
              tokens: data.data.tokens,
              error: null,
            })
          } else {
            throw new Error('Invalid response format')
          }
        } catch (error) {
          // If refresh fails, logout the user
          get().logout()
          throw error
        }
      },

      clearError: () => set({ error: null }),
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
