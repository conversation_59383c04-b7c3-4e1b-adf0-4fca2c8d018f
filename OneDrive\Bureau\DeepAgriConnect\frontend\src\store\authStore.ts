import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: 'admin' | 'farmer' | 'buyer' | 'cooperative' | 'logistics'
  isEmailVerified: boolean
  avatar?: string
  phone?: string
  address?: string
  city?: string
  country?: string
  zipCode?: string
  isActive: boolean
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role: 'farmer' | 'buyer' | 'cooperative' | 'logistics'
  phone?: string
  address?: string
  city?: string
  country?: string
  zipCode?: string
}

interface AuthState {
  user: User | null
  tokens: AuthTokens | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null })

          // Mock authentication for testing
          if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
            const mockUser: User = {
              id: '1',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Farmer',
              role: 'farmer',
              isEmailVerified: true,
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }

            const mockTokens: AuthTokens = {
              accessToken: 'mock-access-token',
              refreshToken: 'mock-refresh-token'
            }

            set({
              user: mockUser,
              tokens: mockTokens,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
            return
          }

          // Try real API call
          const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
          })

          const data = await response.json()

          if (!response.ok) {
            throw new Error(data.message || 'Invalid email or password')
          }

          if (data.success && data.data) {
            set({
              user: data.data.user,
              tokens: data.data.tokens,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            throw new Error('Invalid response format')
          }
        } catch (error) {
          // If API fails, check for mock credentials
          if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
            const mockUser: User = {
              id: '1',
              email: '<EMAIL>',
              firstName: 'John',
              lastName: 'Farmer',
              role: 'farmer',
              isEmailVerified: true,
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }

            const mockTokens: AuthTokens = {
              accessToken: 'mock-access-token',
              refreshToken: 'mock-refresh-token'
            }

            set({
              user: mockUser,
              tokens: mockTokens,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
            return
          }

          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          })
          throw error
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true, error: null })

          // Mock registration for testing
          const mockUser: User = {
            id: String(Date.now()),
            email: data.email,
            firstName: data.firstName,
            lastName: data.lastName,
            role: data.role,
            isEmailVerified: true,
            isActive: true,
            phone: data.phone,
            address: data.address,
            city: data.city,
            country: data.country,
            zipCode: data.zipCode,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }

          const mockTokens: AuthTokens = {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token'
          }

          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 1000))

          set({
            user: mockUser,
            tokens: mockTokens,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })

          // Try real API call in background (optional)
          try {
            const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            })

            const result = await response.json()

            if (response.ok && result.success && result.data) {
              set({
                user: result.data.user,
                tokens: result.data.tokens,
              })
            }
          } catch (apiError) {
            console.log('API registration failed, using mock data:', apiError)
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Registration failed',
          })
          throw error
        }
      },

      logout: async () => {
        try {
          const { tokens } = get()
          
          // Call logout endpoint if we have tokens
          if (tokens) {
            await fetch(`${API_BASE_URL}/api/auth/logout`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${tokens.accessToken}`,
              },
            })
          }
        } catch (error) {
          console.error('Logout API call failed:', error)
        } finally {
          // Clear state regardless of API call success
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            error: null,
          })
        }
      },

      refreshToken: async () => {
        try {
          const { tokens } = get()
          
          if (!tokens?.refreshToken) {
            throw new Error('No refresh token available')
          }

          const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ refreshToken: tokens.refreshToken }),
          })

          const data = await response.json()

          if (!response.ok) {
            throw new Error(data.message || 'Token refresh failed')
          }

          if (data.success && data.data?.tokens) {
            set({
              tokens: data.data.tokens,
              error: null,
            })
          } else {
            throw new Error('Invalid response format')
          }
        } catch (error) {
          // If refresh fails, logout the user
          get().logout()
          throw error
        }
      },

      clearError: () => set({ error: null }),
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
