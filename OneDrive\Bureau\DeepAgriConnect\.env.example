# Database Configuration
DATABASE_URL=./data/agriconnect.db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Weather API Configuration
WEATHER_API_KEY=your-openweathermap-api-key
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# Blockchain Configuration
BLOCKCHAIN_NETWORK=ethereum-testnet
BLOCKCHAIN_RPC_URL=https://sepolia.infura.io/v3/your-project-id
BLOCKCHAIN_PRIVATE_KEY=your-private-key

# AI Service Configuration
AI_SERVICE_URL=http://localhost:5000
AI_API_KEY=your-ai-service-api-key

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Application Configuration
NODE_ENV=development
PORT=4000
CORS_ORIGIN=http://localhost:3000
API_RATE_LIMIT=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Payment Configuration (Simulation)
PAYMENT_GATEWAY_URL=https://api.stripe.com/v1
PAYMENT_SECRET_KEY=sk_test_your_stripe_secret_key
PAYMENT_WEBHOOK_SECRET=whsec_your_webhook_secret

# IoT Configuration
IOT_MQTT_BROKER=mqtt://localhost:1883
IOT_MQTT_USERNAME=agriconnect
IOT_MQTT_PASSWORD=your-mqtt-password

# Notification Configuration
PUSH_NOTIFICATION_KEY=your-push-notification-key
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.twilio.com/2010-04-01
