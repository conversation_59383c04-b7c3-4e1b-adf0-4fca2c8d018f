'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/authStore'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export default function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const router = useRouter()
  const { isAuthenticated, tokens } = useAuthStore()

  useEffect(() => {
    // Simple check: if not authenticated and no tokens, redirect to login
    if (!isAuthenticated && !tokens) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, tokens, router])

  // If not authenticated, show loading or redirect
  if (!isAuthenticated) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
            <p className="mt-2 text-sm text-gray-600">Checking authentication...</p>
          </div>
        </div>
      )
    )
  }

  // If authenticated, render children
  return <>{children}</>
}
