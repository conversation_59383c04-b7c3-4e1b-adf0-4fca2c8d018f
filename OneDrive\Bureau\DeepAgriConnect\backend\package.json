{"name": "agriconnect-backend", "version": "1.0.0", "description": "AgriConnect Backend API - Agricultural platform backend with Express.js and TypeScript", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "node dist/database/migrate.js", "seed": "node dist/database/seed.js", "db:reset": "rm -f data/agriconnect.db && npm run migrate && npm run seed", "typecheck": "tsc --noEmit"}, "keywords": ["agriculture", "api", "express", "typescript", "farm-management", "marketplace", "iot", "blockchain"], "author": "AgriConnect Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "sqlite3": "^5.1.6", "socket.io": "^4.7.4", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "axios": "^1.6.2", "node-cron": "^3.0.3", "redis": "^4.6.10", "ioredis": "^5.3.2", "web3": "^4.3.0", "ethers": "^6.8.1", "mqtt": "^5.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/node-cron": "^3.0.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}